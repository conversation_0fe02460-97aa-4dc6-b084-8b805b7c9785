using UnityEngine;
using System;
using Unity.Netcode;
public class GameManager : NetworkBehaviour
{

    public static GameManager Instance { get; private set; }

    public event EventHandler<OnClickedOnGridPositionEventArgs> OnClickedOnGridPosition;
    public class OnClickedOnGridPositionEventArgs : EventArgs
    {
        public int x;
        public int y;
        public PlayerType playerType;

    }

    private PlayerType localPlayerType;
    public enum PlayerType { Cross, Circle };


    private void Awake()
    {
        if (Instance != null)
        {
            Debug.LogError("Multiple GameManager instances found!");
            return;
        }
        Instance = this;
    }

    public override void OnNetworkSpawn()
    {
        if (NetworkManager.Singleton.LocalClientId == 0)
        {   
            Debug.Log("Player 1");
            localPlayerType = PlayerType.Cross;
        }
        else 
        {
            Debug.Log("Player 2");
            localPlayerType = PlayerType.Circle;
        }
    }

    public void ClickedOnGridPosition(int x, int y)
    {
        OnClickedOnGridPosition?.Invoke(this, new OnClickedOnGridPositionEventArgs
        {
            x = x,
            y = y,
            playerType = GetLocalPlayerType()
        });
    }
    
    public PlayerType GetLocalPlayerType()
    {
        return localPlayerType;
    }

}
