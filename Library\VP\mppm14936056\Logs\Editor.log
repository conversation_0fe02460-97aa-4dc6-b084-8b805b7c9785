[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
Built from '6000.2/staging' branch; Version is '6000.2.6f1 (cc51a95c0300) revision 13390249'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-camil" at "2025-10-06T00:47:17.1993394Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'es' Physical Memory: 32598 MB
BatchMode: 0, IsHumanControllingUs: 1, StartBugReporterOnCrash: 1, Is64bit: 1
Date: 2025-10-06T00:47:17Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Unity.exe
-editor-mode
com.unity.mppm.clone
-no-cloud-project-bind-popup
-noLaunchScreen
-library-redirect
../..
-readonly
-noUpm
-upmRestorePackages
-name
Player 2
-projectPath
C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056
--virtual-project-clone
-forgetProjectPath
-library-redirect
../..
-readonly
-DisableDirectoryMonitor
-noUpm
-upmRestorePackages
-noMainWindow
-suppressDefaultMenuEntries
-logFile
C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056\Logs\Editor.log
-vp-channel-name=vp-channel
-ump-channel-service-port
50161
-mainProcessId=21608
-vpId=mppm14936056
-buildTarget
StandaloneWindows64
-standaloneBuildSubtarget
Player
Successfully changed project path to: C:\Users\<USER>\TicTacToe\Library\VP\mppm14936056
C:/Users/<USER>/TicTacToe/Library/VP/mppm14936056
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25708]  Target information:

Player connection [25708]  * "[IP] *********** [Port] 55505 [Flags] 2 [Guid] 1017467902 [EditorId] 1017467902 [Version] 1048832 [Id] WindowsEditor(7,Camilo-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25708]  * "[IP] *********** [Port] 55505 [Flags] 2 [Guid] 1017467902 [EditorId] 1017467902 [Version] 1048832 [Id] WindowsEditor(7,Camilo-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25708] Host joined multi-casting on [***********:54997]...
Player connection [25708] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 7048, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.3+a4e98f1
  Session Id:              7036cb91ba124e3fbaaf64a57a2cdf0c
  Correlation Id:          489e70f3de3d89558a8dab675c636732
  External correlation Id: 4020877857620541862
  Machine Id:              H/eV6RgZrlsD6oHPYwHyIJhNpFc=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-camil" (connect: 0.00s, validation: 0.00s, handshake: 0.03s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-camil-notifications" at "2025-10-06T00:47:17.2301658Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 4673220025021-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
[Licensing::Client] Successfully updated license, isAsync: True, time: 0.00
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Licensing Background thread has ended after 0.05s
Library Redirect Path: ../../
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Targeting platform: StandaloneWindows64
Refreshing native plugins compatible for Editor in 0.61 ms, found 0 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.6f1 (cc51a95c0300)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/TicTacToe/Library/VP/mppm14936056/Assets
GfxDevice: creating device client; kGfxThreadingModeSplitJobs
d3d12: failed to query info queue interface (0x80004002).
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:          NVIDIA
    VRAM:            16109 MB
    App VRAM Budget: 15341 MB
    Driver:          32.0.15.8115
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56332
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001917 seconds.
- Loaded All Assemblies, in  0.338 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.304 seconds
Domain Reload Profiling: 640ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (144ms)
		LoadAssemblies (104ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (140ms)
				TypeCache.ScanAssembly (128ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (305ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (111ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
Package Manager log level set to [2]
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 69 packages:
  Packages from [https://packages.unity.com]:
    com.unity.2d.animation@12.0.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.animation@34e0443c58ed)
    com.unity.2d.aseprite@2.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.aseprite@f6e7e126ac6d)
    com.unity.2d.psdimporter@11.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.psdimporter@0adcab25a8fd)
    com.unity.2d.spriteshape@12.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.spriteshape@1d246726c231)
    com.unity.2d.tilemap.extras@5.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.tilemap.extras@2338d989ff2a)
    com.unity.collab-proxy@2.9.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.collab-proxy@ab839cc7d2ad)
    com.unity.ide.rider@3.0.38 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ide.rider@1f60b3138684)
    com.unity.ide.visualstudio@2.0.23 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13)
    com.unity.inputsystem@1.14.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.inputsystem@be6c4fd0abf5)
    com.unity.multiplayer.playmode@1.6.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.playmode@3acaa705f0f9)
    com.unity.multiplayer.tools@2.2.6 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.tools@8395f16a9a9a)
    com.unity.netcode.gameobjects@2.5.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.netcode.gameobjects@60f197570e52)
    com.unity.timeline@1.8.9 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.timeline@6b9e48457ddb)
    com.unity.visualscripting@1.9.7 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.visualscripting@6279e2b7c485)
    com.unity.transport@2.5.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.transport@b61461f89a69)
    com.unity.nuget.mono-cecil@1.11.5 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.nuget.mono-cecil@d78732e851eb)
    com.unity.burst@1.8.24 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.burst@f7a407abf4d5)
    com.unity.collections@2.5.7 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.collections@d49facba0036)
    com.unity.mathematics@1.3.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.mathematics@8017b507cc74)
    com.unity.profiling.core@1.0.2 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.profiling.core@aac7b93912bc)
    com.unity.nuget.newtonsoft-json@3.2.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0)
    com.unity.2d.common@11.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.common@dd402daace1b)
    com.unity.searcher@4.9.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.searcher@1e17ce91558d)
    com.unity.test-framework.performance@3.1.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.sprite@331abd9a1e2f)
    com.unity.2d.tilemap@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.2d.tilemap@aa5f5491174d)
    com.unity.multiplayer.center@1.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546)
    com.unity.render-pipelines.universal@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.universal@1e1527b3a17b)
    com.unity.test-framework@1.6.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.test-framework@40b8da2bd3e9)
    com.unity.ugui@2.0.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ugui@e375ff18e90f)
    com.unity.modules.accessibility@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director)
    com.unity.modules.imageconversion@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture)
    com.unity.modules.terrain@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr)
    com.unity.modules.subsystems@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems)
    com.unity.modules.hierarchycore@1.0.0 (location: C:\Program Files\Unity\Hub\Editor\6000.2.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore)
    com.unity.ext.nunit@2.0.5 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.ext.nunit@031a54704bff)
    com.unity.render-pipelines.core@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.core@317e801bb3aa)
    com.unity.shadergraph@17.2.0 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.shadergraph@5516e0d97518)
    com.unity.render-pipelines.universal-config@17.0.3 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.render-pipelines.universal-config@8dc1aab4af1d)
    com.unity.rendering.light-transport@1.0.1 (location: C:\Users\<USER>\TicTacToe\Library\PackageCache\com.unity.rendering.light-transport@2c9279f90d7c)
[Subsystems] No new subsystems found in resolved package list.
[Package Manager] Done registering packages in 0.04 seconds
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.821 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.398 seconds
Domain Reload Profiling: 4213ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (554ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (196ms)
				TypeCache.ScanAssembly (182ms)
			BuildScriptInfoCaches (73ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (3399ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3247ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (2815ms)
			ProcessInitializeOnLoadMethodAttributes (237ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Asset Pipeline Refresh (id=677939bc0bb189f41af3c0b0dfab20fe): Total: 4.836 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=3975 ms
		Asset DB Callback time: managed=0 ms, native=37 ms
		Scripting: domain reloads=1, domain reload time=821 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 37.424ms
	ApplyChangesToAssetFolders: 0.243ms
	CategorizeAssets: 63.347ms
	ImportOutOfDateAssets: 3409.534ms (3406.274ms without children)
		CompileScripts: 1.100ms
		ReloadNativeAssets: 0.024ms
		UnloadImportedAssets: 0.914ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.742ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.479ms
	Hotreload: 1.361ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.003ms
	UnloadStreamsBegin: 0.208ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.789ms
	Untracked: 1323.565ms

Application.AssetDatabase Initial Refresh End
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Scanning for USB devices : 13.366ms
Initializing Unity extensions:
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7971 unused Assets / (12.4 MB). Loaded Objects now: 8759.
Memory consumption went from 237.5 MB to 225.1 MB.
Total: 24.031000 ms (FindLiveObjects: 0.596100 ms CreateObjectMapping: 0.578600 ms MarkObjects: 17.415600 ms  DeleteObjects: 5.439000 ms)

[Project] Loading completed in 7.235 seconds
	Project init time: 				6.795 seconds
		Template init time: 		0.000 seconds
		Package Manager init time: 		0.000 seconds
		Asset Database init time: 		0.107 seconds
		Global illumination init time: 	0.002 seconds
		Assemblies load time: 			0.658 seconds
		Unity extensions init time: 	0.041 seconds
		Asset Database refresh time: 	4.836 seconds
	Scene opening time: 			0.380 seconds
##utp:{"type":"ProjectInfo","version":2,"phase":"Immediate","time":1759711644407,"processId":22332,"projectLoad":7.2348308999999999,"projectInit":6.795107,"templateInit":0.0,"packageManagerInit":5e-7,"assetDatabaseInit":0.1067864,"globalIlluminationInit":0.00221,"assembliesLoad":0.6583416,"unityExtensionsInit":0.0410036,"assetDatabaseRefresh":4.8358069,"sceneOpening":0.3798}
##utp:{"type":"EditorInfo","version":2,"phase":"Immediate","time":1759711644407,"processId":22332,"editorVersion":"6000.2.6f1 (cc51a95c0300)","branch":"6000.2/staging","buildType":"Release","platform":"Windows"}
Asset Pipeline Refresh (id=92395cd6d2041894683fa7cf9278a8d7): Total: 0.896 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=895 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.213ms
	CategorizeAssets: 94.674ms
	ImportOutOfDateAssets: 9.644ms (5.188ms without children)
		ReloadNativeAssets: 0.024ms
		UnloadImportedAssets: 0.003ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.675ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.001ms
		OnDemandSchedulerStart: 3.753ms
	Hotreload: 1.931ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.217ms
	UnloadStreamsBegin: 1.256ms
	UnloadStreamsEnd: 0.002ms
	Untracked: 787.929ms

Created GICache directory at C:/Users/<USER>/AppData/LocalLow/Unity/Caches/GiCache. Took: 0.012s, timestamps: [7.237 - 7.250]
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
<RI> Initializing input.

Using Windows.Gaming.Input
<RI> Input initialized.

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
Opening scene 'Assets/Scenes/GameScene.unity'
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Input System polling thread started.
Loaded scene 'Assets/Scenes/GameScene.unity'
	Deserialize:            44.087 ms
	Integration:            306.410 ms
	Integration of assets:  13.752 ms
	Thread Wait Time:       2.018 ms
	Total Operation Time:   366.267 ms
Unloading 7895 unused Assets / (314.2 KB). Loaded Objects now: 8936.
Memory consumption went from 311.4 MB to 311.1 MB.
Total: 23.716900 ms (FindLiveObjects: 0.919000 ms CreateObjectMapping: 1.070900 ms MarkObjects: 18.960900 ms  DeleteObjects: 2.764500 ms)

[Licensing::Client] Successfully updated the access token
[Licensing::Module] Successfully updated access token: "QWJlryov"... (expires: 2025-10-06T00:56:09Z)
SpriteAtlasPacking completed in 0.003892 sec
Reloading assemblies for play mode.
Reloading assemblies after forced synchronous recompile.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.339 seconds
Refreshing native plugins compatible for Editor in 2.35 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.181 seconds
Domain Reload Profiling: 3519ms
	BeginReloadAssembly (746ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (507ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (513ms)
		LoadAssemblies (367ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (234ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2182ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1594ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (189ms)
			ProcessInitializeOnLoadAttributes (1254ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (222ms)
Asset Pipeline Refresh (id=6763fc9c06da0534abaf75af7a43ba50): Total: 4.313 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2953 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1359 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.003ms
	ApplyChangesToAssetFolders: 0.362ms
	CategorizeAssets: 91.880ms
	ImportOutOfDateAssets: 2193.456ms (2188.791ms without children)
		CompileScripts: 1.075ms
		CollectScriptTypesHashes: 1.484ms
		ReloadNativeAssets: 0.764ms
		UnloadImportedAssets: 0.106ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.893ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.342ms
	Hotreload: 0.225ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.188ms
	UnloadStreamsBegin: 1.389ms
	UnloadStreamsEnd: 0.002ms
	GenerateScriptTypeHashes: 3.771ms
	Untracked: 2025.960ms

Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.703 ms
	Integration:            326.770 ms
	Integration of assets:  0.002 ms
	Thread Wait Time:       0.011 ms
	Total Operation Time:   328.486 ms
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0031.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
UPID Received '98b157b4-afc1-4451-8646-e67cfa6c5848'.
ClickedOnGridPosition - ClientId: 1, PlayerType: Cross
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GameManager:ClickedOnGridPosition (int,int) (at C:/Users/<USER>/TicTacToe/Assets/Scripts/GameManager.cs:48)
GridPosition:OnMouseDown () (at C:/Users/<USER>/TicTacToe/Assets/Scripts/GridPosition.cs:17)
UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
UnityEngine.SendMouseEvents/HitInfo:SendMessage (string)
UnityEngine.SendMouseEvents:SendEvents (int,UnityEngine.SendMouseEvents/HitInfo)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

(Filename: C:/Users/<USER>/TicTacToe/Assets/Scripts/GameManager.cs Line: 48)

ClickedOnGridPosition - ClientId: 1, PlayerType: Cross
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
GameManager:ClickedOnGridPosition (int,int) (at C:/Users/<USER>/TicTacToe/Assets/Scripts/GameManager.cs:48)
GridPosition:OnMouseDown () (at C:/Users/<USER>/TicTacToe/Assets/Scripts/GridPosition.cs:17)
UnityEngine.GameObject:SendMessage (string,object,UnityEngine.SendMessageOptions)
UnityEngine.SendMouseEvents/HitInfo:SendMessage (string)
UnityEngine.SendMouseEvents:SendEvents (int,UnityEngine.SendMouseEvents/HitInfo)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

(Filename: C:/Users/<USER>/TicTacToe/Assets/Scripts/GameManager.cs Line: 48)

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
TrimDiskCacheJob: Current cache size 0mb
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            1.207 ms
	Integration:            243.434 ms
	Integration of assets:  0.001 ms
	Thread Wait Time:       0.015 ms
	Total Operation Time:   244.656 ms
Unloading 7895 unused Assets / (4.0 MB). Loaded Objects now: 9928.
Memory consumption went from 425.0 MB to 420.9 MB.
Total: 63.594800 ms (FindLiveObjects: 0.659700 ms CreateObjectMapping: 0.584000 ms MarkObjects: 59.676100 ms  DeleteObjects: 2.673900 ms)

[Licensing::Client] Successfully resolved entitlement details
Asset Pipeline Refresh (id=8a2b8562ea91b5443bb983a724600602): Total: 0.434 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=434 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.205ms
	CategorizeAssets: 64.796ms
	ImportOutOfDateAssets: 1.048ms (0.289ms without children)
		ReloadNativeAssets: 0.024ms
		UnloadImportedAssets: 0.001ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.430ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.304ms
	Hotreload: 2.180ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.277ms
	UnloadStreamsBegin: 0.050ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 365.783ms

[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0031.wlt
[LAYOUT] About to load Library/UserSettings/Layouts\layout_0010.wlt, keepMainWindow=False
[LAYOUT] About to save layout Library/UserSettings/Layouts\layout_0010.wlt
Reloading assemblies due to reload request.
Reloading assemblies after forced synchronous recompile.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.143 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-TicTacToe
[MODES] Loading editor mode Multiplayer Playmode Clone (1) from command line.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.942 seconds
Domain Reload Profiling: 3085ms
	BeginReloadAssembly (632ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (440ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (430ms)
		LoadAssemblies (322ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (160ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1942ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (221ms)
			ProcessInitializeOnLoadAttributes (976ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (213ms)
Asset Pipeline Refresh (id=805d3f104521fe34daee4358bf68e3ff): Total: 3.757 seconds - Initiated by StopAssetImportingV2(ForceSynchronousImport | ForceDomainReload)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=2596 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1159 ms, compile time=1 ms, other=0 ms
		Project Asset Count: scripts=1795, non-scripts=3074
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 0
	InvokeCustomDependenciesCallbacks: 0.000ms
	InvokePackagesCallback: 0.003ms
	ApplyChangesToAssetFolders: 0.345ms
	CategorizeAssets: 79.516ms
	ImportOutOfDateAssets: 1951.836ms (1948.280ms without children)
		CompileScripts: 1.026ms
		CollectScriptTypesHashes: 0.615ms
		ReloadNativeAssets: 0.789ms
		UnloadImportedAssets: 0.095ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.679ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.001ms
		OnDemandSchedulerStart: 0.351ms
	Hotreload: 1.478ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.206ms
	UnloadStreamsBegin: 0.002ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 2.774ms
	Untracked: 1723.631ms

