%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d777d270cee3a5c419cb21a828556f1a, type: 3}
  m_Name: CodeMonkeyFreeSO
  m_EditorClassIdentifier: 
  currentVersion: 1.00
  subtype: tictactoengofree
  lastShownTimestamp: 1759711119
  lastUpdateResponse:
    version: 1.00
    versionUrl: https://unitycodemonkey.com
  checkedLastUpdateTimestamp: 1759711119
  lastQOTDResponse:
    questionId: 228
    questionText: What does Normalizing a Vector do?
    answerA: Modifies its x,y,z to get a magnitude of 1
    answerB: Invert its x,y,z
    answerC: Flatten the z
    answerD: 
    answerE: 
  lastQotdTimestamp: 1759711119
  lastDynamicHeaderResponse:
    topImageUrl: https://codemonkeyexternal.blob.core.windows.net/external/Misc/humbleSep25Synty.png
    topText: After 1 year there's a new Synty Humble Bundle! DON'T MISS IT!
    topLink: humblebundle
    bottomText: My 10+ Years of C# Knowledge in 1 Course!
    bottomLink: csharpcourse
  lastDynamicHeaderTimestamp: 1759711119
  websiteLatestMessage:
    text: <a href="https://cmonkey.co/freecourses">Check out all my FREE courses!
      Click here!</a>
  websiteLatestMessageTimestamp: 1759711119
  websiteLatestVideos:
    videos:
    - youTubeId: Q59s-VS684o
      title: MASSIVE Unity Security Vulnerability! (but also nothing to worry about)
    - youTubeId: vhflNElicXU
      title: Optimization is awesome, but do NOT make this mistake!
    - youTubeId: _UadeZn6kI8
      title: Should you use UI Toolkit or Unity UI?
    - youTubeId: E7OBm60vUCY
      title: BEST NEW Visual Assets to INSPIRE YOU!
  websiteLatestVideosTimestamp: 1759711119
